import pandas as pd
from pyspark.sql import SparkSession
import pandas as pd
import requests
import time
import json
import logging
import sys
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
import re
import math
import numpy as np
logger = logging.getLogger(__name__)


spark = SparkSession.builder.appName("bdms_chenhan04")\
                    .config("spark.debug.maxToStringFields","1000")\
                    .config("spark.driver.maxResultSize", "30g")\
                    .enableHiveSupport()\
                    .getOrCreate()

def llm_chat(senetence):
    url = 'http://127.0.0.1:8550/proxy/online.yanxuan-nlp-server.service.mailsaas/openai/chat'
    headers = {
        'Content-Type': 'application/json',
        'product': 'mock',
        'service': 'deepseek-r1',
        'timeout': '600000'
    }
    payload = {
        "messages": [
            {
                "role": "user",
                "content": senetence
            }
        ],
        "model": "deepseek-r1",
        "maxTokens": 10001,
        "temperature": 0.3,
        "topP": 1,
        "presencePenalty": 0,
        "frequencyPenalty": 0
    }

    response = requests.post(url, json=payload, headers=headers)

    return json.loads(response.text)


### 三级分类聚类
prompt_cluster="""

角色: 你是专业的数据分析和电商运营人员

简介: 你是一个专业的电商运营人员，我会给你几个用户会话分类，有些分类是比较相似的，我需要你对这些分类类别中相似的内容做聚类，
并将聚类之后的一系列会话的分类总结成一个新的聚类类别,新的聚类名称优先从给定的列表中选择,这个生成的聚类类别可以是原类别也可以是你加工过的，
生成的问题需要从用户视角出发，要像一个用户会问出来的问题

分析流程
将相似的用户会话分类聚类，聚类类别尽最大可能从{}中存在的类别进行选择，如果列表中没有，再基于相似的类别进行总结

注意事项
1. 聚类结果进行总结并输出为新需求,汇总的需求需要以问句的形式给出，如果无法使用问句，再使用陈述句
2. 尽可能多的将会话进行聚类，减少一个聚类下只有一个会话的情况
3. 生成的疑问句需要以用户角度，需要像一个用户真实情况会问出来的问题，这一点很重要
4. 聚类之后生成的问题尽可能的聚焦，一个聚类类别尽量不出现逗号，也不要出现多个问句,并且字数一定不得超过20个字（严格遵守这个规定，超过20个字需要合理删减）
5. 同一个会话可以出现在多个聚类当中

会话内容:
商品名称:{}
需求内容:{}

输出形式:
输出结果一定以json形式给出，格式和包括的字段如下
{{
"session_analysis":[{{"cate3_new":"","cate3_raw":[{{"session_id":"","cate3":""}}]}},...,...]
}}

注意：上面的输出，cate3_new字段为显得聚类之后总结的类别，且字数不超过20字（严格遵守这个规定，超过20个字需要合理删减），cate3_raw是该聚类下的原来的类别，session_id和cate3都是原来的
"""
# 获取之前一周的该商品下的聚类意图，用于记忆往期,同时还需要客服审批之后的聚类标准
def cluster_cate_ago(channel,item_name,ds,day_ago=7):
    
    date_ago = (datetime.strptime(ds, "%Y-%m-%d") - timedelta(days=day_ago)).strftime("%Y-%m-%d")
    
    sql="""
    select
      distinct cate1,cate2,cluster_cate
    from
      data_mining.kefu_session_xd_cluster_category_final
    where
      ds > "{}" and ds>"2025-05-01" and ds< "{}"
      and item_name = "{}"
      and channel = "{}"

    """.format(date_ago,ds,item_name,channel)
    
    pd=spark.sql(sql).toPandas()
    return pd

def extract_user_messages(session_data):
    user_messages = []
    for line in session_data.split('\n'):
        if line.startswith('用户:'):
            # 直接保留完整消息（包含用户前缀）
            user_messages.append(line.strip())
    return '\n'.join(user_messages)

def query_parser(sentence):
    # 去除对话前面的数字
    if '\r\n' in sentence:
        dialogue_part= sentence.split('\r\n')
        processed_lines = [re.sub(r'^\d{4}', '', line) for line in dialogue_part]
        processed_lines = [re.sub(r'https?://\S+|www\.\S+', '...', line) for line in processed_lines]
        qp_sen="\n".join(processed_lines)
        return qp_sen
    return sentence

def cate123(ds,channel_en):
    sql="""
        select * from data_mining.kefu_session_xd_category123 where ds="{}" and channel="{}"
    """.format(ds,channel_en)
    df=spark.sql(sql).toPandas()
    return df

# 考虑多线程任务执行
def cluster_task(cate123_pd,item_id,channel,ds):
    # cate123_pd:一二三级分类表
    cate123_list = cate123_pd.loc[cate123_pd["item_id"]==item_id].to_dict(orient='records')
    item_name=cate123_pd.loc[cate123_pd["item_id"]==int(item_id)][["item_name"]]["item_name"].unique().tolist()[0]
    # 将数据进行汇总
    cate_collections={}
    for cate in cate123_list:
        cate_key="{}-{}".format(cate["cate1"],cate["cate2"])
        if cate_key in cate_collections:
            cate_collections[cate_key].append({"session_id":cate["session_id"],"cate3":cate["cate3"]})
        else:
            cate_collections[cate_key]=[{"session_id":cate["session_id"],"cate3":cate["cate3"]}]

    cluster_ago=cluster_cate_ago(channel,item_name,ds)
    cluster_cate3=[]
    for cate12 in cate_collections.keys():
        cates_list=cate_collections[cate12]
        cate1,cate2=cate12.split("-")[0],cate12.split("-")[1]
        cluster_ago_list=cluster_ago.loc[(cluster_ago["cate1"]==cate1)&(cluster_ago["cate2"]==cate2)]["cluster_cate"].unique().tolist()
        print("往期聚类意图{}:{}".format(cate12,cluster_ago_list))
        prompt_cluster_full=prompt_cluster.format(cluster_ago_list,item_name,cates_list)
        try:
#             response_cluster=llm_chat(sys_promot,prompt_cluster_full)
            response_cluster=llm_chat(prompt_cluster_full)
            response_cluster_txt=response_cluster['data']["detail"]["choices"][0]["message"]["content"]
            # response_cluster_list=json.loads(response_cluster_txt[response_cluster_txt.find("["):response_cluster_txt.rfind("]")+1])
            response_cluster_list=json.loads(response_cluster_txt[response_cluster_txt.find("{"):response_cluster_txt.rfind("}")+1])
            print("llm大模型结果:{}".format(response_cluster_list["session_analysis"]))
            cluster_cate3+=response_cluster_list["session_analysis"]
            time.sleep(3)
        except Exception as e:
            continue

    # 将聚类后的分类和原来的分类汇总
    # rows=[]
    for cate in cate123_list:
        rows=[]
        for new_cate in cluster_cate3:
            for cate3_raw in new_cate["cate3_raw"]:
                if int(cate["session_id"])==int(cate3_raw["session_id"]) and cate["cate3"]==cate3_raw["cate3"]:
                    rows.append({
                        "session_id":cate["session_id"],
                        "cate1":cate["cate1"],
                        "cate2":cate["cate2"],
                        "cate3":cate["cate3"],
                        "VOC":cate["voc"],
                        "cluster_cate":new_cate["cate3_new"],
                        "channel":channel,
                        "item_name":item_name,
                        "item_id":item_id
                    })

        # 将结果写入表里
        if len(rows)==0:
            return
        df = pd.DataFrame(rows).dropna()
        df=df[["item_id","item_name","session_id","cate1","cate2","cate3","VOC","cluster_cate"]]
        df["session_id"]=df["session_id"].astype(int)
        df["VOC"]=df["VOC"].astype("string")
    
    
        solution_sp=spark.createDataFrame(df)
        solution_sp.createOrReplaceTempView("data_solution")
        sql0="""insert into table data_mining.kefu_session_xd_cluster_category PARTITION (ds="{}",channel="{}")
                select * from data_solution""".format(ds,channel_en)
        spark.sql(sql0)
    return rows

if __name__ == "__main__":
#     source_table="data_mining.kf_dy_shop_xd_detail_topn"
#     source_table=sys.argv[1]
#     channel="抖快系"
    channel=sys.argv[1]
#     channel_en="dy"
    channel_en=sys.argv[2]
#     ds="2025-03-26"
    ds=sys.argv[3]
    cate123_pd=cate123(ds,channel_en)
    item_ids=cate123_pd["item_id"].unique().tolist()
    
    # 每次重跑，删除之前的数据
    sql_dd="""ALTER TABLE data_mining.kefu_session_xd_cluster_category DROP IF EXISTS PARTITION (ds="{}",channel="{}")""".format(ds,channel_en)
    spark.sql(sql_dd)
    
    with ThreadPoolExecutor(max_workers=10) as executor:
        # 提交任务
        futures = [executor.submit(cluster_task, cate123_pd,item_id,channel_en,ds) for item_id in item_ids]
        # 获取结果
        for future in futures:
            print(future.result())