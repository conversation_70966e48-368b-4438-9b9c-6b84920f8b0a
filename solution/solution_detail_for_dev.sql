-- ******************************************************************** --
-- Author: 陈晗
-- CreateTime: 2025-06-19 16:49:48
-- Comment: 请输入业务注释信息
-- ******************************************************************** --
insert
  OVERWRITE table data_mining.kefu_session_xd_cluster_category_solutions_kf PARTITION (ds = "${azkaban.flow.1.days.ago}", channel)
select
  DISTINCT row_number() over(
    order by
      1
  ) as _id,
  a.*
from
  (
    select
      distinct t1.train_id,
      t1.item_id,
      t1.item_name,
      t3.level1_category_id as cate1_id,
      t2.cate1,
      t3.level2_category_id as cate2_id,
      t2.cate2,
      t1.cluster_cate,
      t1.solution_type,
      t1.solution_id,
      t1.solutions,
      t1.bot_response,
      t1.channel
    from
      temp.temp_kefu_session_xd_cluster_category_solutions_update_kf t1
      left join (
        select
          *
        from
          data_mining.kefu_session_xd_cluster_category_final
        where
          ds = "${azkaban.flow.1.days.ago}"
      ) t2 on t1.channel = t2.channel
      and t1.item_name = t2.item_name
      and t1.cluster_cate = t2.cluster_cate
      left join (
        select
          distinct level1_category_id,
          level2_category_id,
          level1_category_name,
          level2_category_name
        from
          dim.dim_user_kf_ticket_category_p
        where
          ds = "${azkaban.flow.1.days.ago}"
          and status = 1
          and current_category_level = 2
      ) t3 on t2.cate1 = t3.level1_category_name
      and t2.cate2 = t3.level2_category_name
    where
      t1.item_id is not NULL and t3.level2_category_id is not NULL
  ) a