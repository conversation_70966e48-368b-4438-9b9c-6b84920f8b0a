-- ******************************************************************** --
-- Author: 陈晗
-- CreateTime: 2025-05-15 15:56:00
-- Comment: 请输入业务注释信息，记录当下所有的train_id
-- ******************************************************************** --
insert
  OVERWRITE table dim.dim_kf_train_task_xd_cluster PARTITION (ds = "${azkaban.flow.1.days.ago}")
select
  distinct train_id_raw,
  channel,
  cluster_cate,
  item_id,
  "无" as solution_type,
  first_ds,
  is_check
from
  (
    select
      train_id_raw,
      channel,
      cluster_cate,
      item_id,
      solution_type,
      first_ds,
      is_check
    from
      dim.dim_kf_train_task_xd_cluster
    where
      ds = "${azkaban.flow.2.days.ago}"
    union all
    select
      t1.train_id as train_id_raw,
      t1.channel,
      t1.cluster_cate,
      t1.item_id,
      t1.solution_type,
      "${azkaban.flow.1.days.ago}" as first_ds,
      0 as is_check
    from
      (
        select
          *
        from
          data_mining.kefu_session_xd_cluster_category_solutions_update
        where
          ds = "${azkaban.flow.1.days.ago}"
      ) t1
      left join (
        select
          train_id_raw,
          channel,
          cluster_cate,
          item_id,
          solution_type,
          first_ds,
          is_check
        from
          dim.dim_kf_train_task_xd_cluster
        where
          ds = "${azkaban.flow.2.days.ago}"
      ) t2 on t1.cluster_cate = t2.cluster_cate
      and t1.channel = t2.channel
      and t1.item_id = t2.item_id
    where
      t2.cluster_cate is NULL
  ) a