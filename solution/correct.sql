-- ******************************************************************** --
-- Author: 陈晗
-- Comment: 请输入业务注释信息
-- 往期意图纠正，目前纠正过去60天，如有需要可以适当增加
-- ******************************************************************** --
-- 启用动态分区
SET hive.exec.dynamic.partition=true;
SET hive.exec.dynamic.partition.mode=nonstrict;
insert
  OVERWRITE table data_mining.kefu_session_xd_cluster_category_solutions_update PARTITION (ds, channel) -- drop TABLE if EXISTS temp.xd_correction_demo;
  -- create table temp.xd_correction_demo as
select
  distinct nvl(t2.train_id_new, t1.train_id) as train_id,
  t1.item_id,
  t1.item_name,
  nvl(t2.correctedintent, t1.cluster_cate) as cluster_cate,
  t1.solution_type,
  t1.solutions,
  t1.sessions,
  t1.session_ids,
  t1.kf_answer,
  t1.source_type,
  t1.knowledge_id,
  t1.answer_id,
  t1.attr,
  t1.ds,
  t1.channel
from
  (
    select
      *
    from
      data_mining.kefu_session_xd_cluster_category_solutions_update
    where
      ds >= "${azkaban.flow.30.days.ago}"
      and ds < "${azkaban.flow.1.days.ago}"
  ) t1
  left join (
    select
      *
    from
      data_mining.kefu_session_xd_train_id_correction
    where
      ds = "${azkaban.flow.1.days.ago}"
  ) t2 on t1.train_id = t2.train_id_old