-- ******************************************************************** --
-- Author: 陈晗
-- Comment: 请输入业务注释信息
-- ******************************************************************** --
create table
  if not exists data_mining.kf_session_message_content_detail_ch
  (session_id bigint comment '会话id'
   ,session_message_content string comment '会话对话明细'    
 )
 COMMENT 'VOC会话百灵对话明细'
 PARTITIONED BY ( `ds` string COMMENT '日期')
 ROW FORMAT SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'
STORED AS INPUTFORMAT 'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat'
OUTPUTFORMAT 'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat';


INSERT OVERWRITE TABLE data_mining.kf_session_message_content_detail_ch
 PARTITION (ds = '${azkaban.flow.1.days.ago}')
select session_id,
--concat_ws(';',collect_list(session_message_content_join)) as session_message_content
concat_ws('\r\n',sort_array(collect_set(session_message_content_join))) as session_message_content
from (
select session_id,
concat(cast(lpad(session_message_sequence,4,'0')as string),session_message_source_new,':',session_message_content) as session_message_content_join,
session_message_sequence,
rn
from (
select session_id,
case when session_message_source=1 then '用户'
when session_message_source=2 then '客服'
end as session_message_source_new,
session_message_content,
session_message_sequence,
row_number() over(
            partition by session_id
            order by
              session_message_sequence asc
          ) as rn
from  dw.dwd_yx_kf_smart_workbench_online_session_message_i
--dw.dwd_yx_kf_smart_workbench_online_session_message_p
where ds>='${azkaban.flow.7.days.ago}'
--ds=max_pt('dw.dwd_yx_kf_smart_workbench_online_session_message_p')
and  to_date(from_unixtime(floor(create_time/1000)))>='${azkaban.flow.7.days.ago}'
) a
order by rn
)a
group by  session_id