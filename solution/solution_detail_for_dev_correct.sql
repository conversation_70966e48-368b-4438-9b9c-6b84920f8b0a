-- ******************************************************************** --
-- Author: 陈晗
-- Comment: 请输入业务注释信息
-- ******************************************************************** ----
SET
  hive.exec.dynamic.partition = true;
SET
  hive.exec.dynamic.partition.mode = nonstrict;
insert
  OVERWRITE table data_mining.kefu_session_xd_cluster_category_solutions_kf PARTITION (ds, channel) -- drop table if EXISTS temp.xd_correction_demo_kf;
  -- create table temp.xd_correction_demo_kf as
select
  DISTINCT row_number() over(
    order by
      1
  ) as _id,
  aa.*
from
  (
    select
      distinct nvl(t2.train_id_new, t1.train_id) as train_id,
      t1.item_id,
      t1.item_name,
      t1.cate1_id,
      t1.cate1,
      t1.cate2_id,
      t1.cate2,
      nvl(t2.correctedintent, t1.cluster_cate) as cluster_cate,
      t1.solution_type,
      t1.sloution_id,
      t1.solutions,
      t1.bot_response,
      t1.ds,
      t1.channel
    from
      (
        select
          *
        from
          data_mining.kefu_session_xd_cluster_category_solutions_kf
        where
          ds >= "${azkaban.flow.30.days.ago}"
          and ds < "${azkaban.flow.1.days.ago}"
      ) t1
      left join (
        select
          *
        from
          data_mining.kefu_session_xd_train_id_correction
        where
          ds = "${azkaban.flow.1.days.ago}"
      ) t2 on t1.train_id = t2.train_id_old
  ) aa