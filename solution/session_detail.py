import pandas as pd
from pyspark.sql import SparkSession
import pandas as pd
import requests
import time
import json
import logging
import sys
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
import re
import math
import numpy as np
logger = logging.getLogger(__name__)


spark = SparkSession.builder.appName("bdms_chenhan04")\
                    .config("spark.debug.maxToStringFields","1000")\
                    .config("spark.driver.maxResultSize", "30g")\
                    .enableHiveSupport()\
                    .getOrCreate()

def parser(test):
    test=re.sub(' ','',test)
    test=re.sub(r'[\"\']','',test)
    test=re.sub('session_id','"session_id"',test)
    test=re.sub('session_content','"session_content"',test)
    test=re.sub(',用户','","用户',test)
    test=re.sub('用户','"用户',test)
    test=re.sub(',客服','","客服',test)
    test=re.sub(']}]','"]}]',test)
    test=re.sub(']},','"]},',test)
    test=re.sub('""','"',test)
    test=re.sub('{"用户','"用户',test)
    test=re.sub('},"用户','","用户',test)
    test=re.sub('}"]}]','"]}]',test)
    test=re.sub('""','"',test)
    return test

def json_transfer(text):
    try:
        json_text=json.loads(text)
    except Exception as e:
        return [{"session_id":0,"session_content":[]}]
    return json_text

def content_pair(content_list):
    pairs=[]
    for message in content_list:
        if message[0:2]=="用户":
            pairs.append({"messageSource":1,"messageContent":message[3:]})
        if message[0:2]=="客服":
            pairs.append({"messageSource":2,"messageContent":message[3:]})
    return pairs

if __name__ == "__main__":
    # yesterday="2025-06-11"
    yesterday=sys.argv[1]
    sql="select * from data_mining.kefu_session_xd_cluster_category_solutions_update where ds='{}' and solution_type='知识库'".format(yesterday)
    
    pd=spark.sql(sql).toPandas()
    pd["parser_column"]=pd["sessions"].apply(lambda x: parser(x))
    pd['json_data'] = pd["parser_column"].apply(lambda x: json_transfer(x))
    pd_exploded = pd.explode('json_data').reset_index(drop=True)
    pd_exploded["session_id"]=pd_exploded["json_data"].apply(lambda x:x.get("session_id"))
    pd_exploded["id"]=1
    pd_exploded["content"]=pd_exploded["json_data"].apply(lambda x:str(content_pair(x.get("session_content"))))
    pd_exploded["train_id_new"]=pd_exploded["train_id"]
    
    df=pd_exploded[["id","train_id_new","session_id","content","channel"]]
    content_df=spark.createDataFrame(df)
    content_df.createOrReplaceTempView("content_df")
    sql0="""insert overwrite table data_mining.kefu_session_xd_content_single PARTITION (ds="{}",channel)
            select * from content_df""".format(yesterday)
    spark.sql(sql0)
    

