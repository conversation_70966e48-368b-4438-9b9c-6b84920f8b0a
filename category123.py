import pandas as pd
from pyspark.sql import SparkSession
import requests
import time
import json
import logging
import sys
from datetime import datetime, timedelta
import re
logger = logging.getLogger(__name__)


spark = SparkSession.builder.appName("bdms_chenhan04")\
                    .config("spark.debug.maxToStringFields","1000")\
                    .config("spark.driver.maxResultSize", "30g")\
                    .enableHiveSupport()\
                    .getOrCreate()

def read_voc(source_table,channel,ds):
    ds_obj = datetime.strptime(ds, "%Y-%m-%d")
    date_7_ago=(ds_obj - timedelta(days=7)).strftime("%Y-%m-%d")
    sql="""
        select
          distinct t1.item_id,t1.shop_name,t1.item_name,t1.session_id,t2.session_message_content
        from
          (
            select
              *
            from
              {}
            where
              ds = "{}"
          ) t1
          left join (
            select
              *
            from
              kefu.kf_session_voc_detail_zll
            where
              ds >= "{}"
              and channel_small_name = "{}"
          ) t2 on t1.session_id = t2.session_id
    """.format(source_table,ds,date_7_ago,channel)
    
    df=spark.sql(sql).toPandas()
    return df

def llm_chat(senetence):
    url = 'http://127.0.0.1:8550/proxy/online.yanxuan-nlp-server.service.mailsaas/openai/chat'
    headers = {
        'Content-Type': 'application/json',
        'product': 'mock',
        'service': 'deepseek-r1',
        'timeout': '600000'
    }
    payload = {
        "messages": [
            {
                "role": "user",
                "content": senetence
            }
        ],
        "model": "deepseek-r1",
        "maxTokens": 10001,
        "temperature": 0.3,
        "topP": 1,
        "presencePenalty": 0,
        "frequencyPenalty": 0
    }

    response = requests.post(url, json=payload, headers=headers)

    return json.loads(response.text)

def extract_user_messages(session_data):
    user_messages = []
    for line in session_data.split('\n'):
        if line.startswith('用户:'):
            # 直接保留完整消息（包含用户前缀）
            user_messages.append(line.strip())
    return '\n'.join(user_messages)

def query_parser(sentence):
    # 去除对话前面的数字
    if '\r\n' in sentence:
        dialogue_part= sentence.split('\r\n')
        processed_lines = [re.sub(r'^\d{4}', '', line) for line in dialogue_part]
        processed_lines = [re.sub(r'https?://\S+|www\.\S+', '...', line) for line in processed_lines]
        qp_sen="\n".join(processed_lines)
        return qp_sen
    return sentence


### 分类会话prompt
cate1=["商品问题","售后服务","活动问题","发货与物流","收货异常","其他"]
cate2={"商品问题":["商品推荐","是否适用","商品对比","属性问题","功能咨询","如何使用","用户个需","库存相关","售后政策","不良反应","质量问题反馈"],
       "售后服务":["需要退换货","维修相关","安装相关","补发配件相关","发票相关","价保相关"],
       "活动问题":["免单活动","返现活动","价格与优惠","国补相关"],
       "发货与物流":["发货时间","快递选择","物流服务"],
       "收货异常":["商品错发","商品漏发","商品多发","商品破损"],
       "其他":["其他"]}

cate3={
    "商品推荐":"枚举用户画像/用户需求场景,例如:厨房/客厅/卫生间哪一款适用？身高xxcm推荐哪个尺码？",
    "是否适用":"枚举用户咨询场景,例如:能否登机?能否可用于宠物窝?",
    "商品对比":"枚举用户需对比组合,例如:A商品和B商品有啥差别？",
    "属性问题":"枚举用户咨询的具体属性名称,例如:商品配料表有xx吗?商品保质期是多久？",
    "功能咨询":"枚举用户咨询的具体功能,例如:是否支持头枕自适应?",
    "如何使用":"枚举用户咨询的具体场景,例如:如何调节亮度?如何找回密码?",
    "用户个需":"枚举用户咨询的具体场景,例如:有没有某个味道的？",
    "库存相关":"枚举用户咨询的具体场景,例如:某某味什么时候补货?",
    "售后政策":"枚举用户咨询的具体场景,例如:有没有运费险？退换货政策是怎么样的？",
    "不良反应":"枚举用户反馈的具体场景,例如:用户反馈吃了呕吐，用户反馈使用后出现过敏",
    "质量问题反馈":"枚举用户咨询的具体场景,例如:该商品无法通电怎么回事",
    "退换货相关":"枚举用户需退换货的原因,例如:大小不合适，要求退换货",
    "维修相关":"枚举用户需维修的原因,例如:不通电，需要维修",
    "安装相关":"枚举用户需安装的部位或指导细节,例如:有无安装视频？这个怎么安装？",
    "补发配件相关":"枚举用户需要补发的配件信息与原因,例如:喷头破损需要补发喷头；猫粮袋破损需要补发包装袋",
    "发票相关":"枚举用户需发票原因,例如:开专票，增值税发票，补发票，发票开具时间咨询等",
    "价保相关":"枚举用户需价保的原因,例如:用户反馈商品降价，要求退差价",
    "免单活动":"枚举用户咨询的具体活动问题,例如:免单名单什么时候公布",
    "返现活动":"枚举用户咨询的场景,例如:好评可以返现吗",
    "价格与优惠":"枚举用户咨询的场景,例如:有什么赠品?咨询当前有什么活动?咨询为什么到手价与描述不一致?",
    "国补相关":"枚举用户咨询的场景,例如:哪些地方有国补?国补可以补贴多少?",
    "发货时间":"枚举用户实际意图,例如:什么时候发货?咨询是否可以当天发货?",
    "快递选择":"枚举用户实际意图,例如:可以发xx快递吗？",
    "物流服务":"枚举用户实际意图,例如:反馈物流停滞需要催促派件；投诉物流不送货上门",
    "商品错发":"枚举用户反馈错发的商品,例如:用户反馈颜色发错",
    "商品漏发":"枚举用户反馈漏发的商品,例如:用户反馈没有收到赠品",
    "商品多发":"枚举用户反馈多发的商品,例如:用户反馈商品多发了几个",
    "商品破损":"枚举用户反馈破损的部位,例如:用户反馈商品破损",
    "其他": "枚举上面没有提到的其他问题"
}

prompt_cate="""

角色: 你是专业的数据分析和电商运营人员

简介: 你是一个专业的数据分析和电商运营人员，可以准确的总结出用户售前咨询场景，并分类到标准分类中。

严格限制：输出结果必须积极健康，切忌任何反动、色情、暴力等回复

分析流程：
你需要对下面的会话内容进行仔细分析（注意会话内容目前只保留了用户的话，避免干扰将客服的回复已经删除），并按照如下流程进行：
1. 分析每个会话，并进行一级和二级分类，一级分类只能从列表{}里面选择，二级分类必须严格依据一级分类，并在这个字典{}里，按照一级分类进行二级分类，一级和二级分类必须要个按照字典映射。
2. 当一级分类和二级分类已经完成，基于这个字典{},内容进行下一步分析，三级分类应该尽可能符合要求，并且形式以文件要求是问题性给出还是陈述形式给出，并且给出的结果尽量是原文中出现的，如果原文中没有你可以发挥一下
3. 每个会话可以有多个分类，不局限为一个，一定注意，三级分类后面的例子仅供参考，绝对不可以直接输出这些例子
4. 按照输出格式给出结果，输出格式在下面


会话内容:
商品名称:{}
会话内容:{}


输出形式:
输出结果以json形式给出，字段如下
{{
session_analysis:[
{{"session_id":"","cate1":"","cate2":"","cate3":"",VOC":["","",""]}},
........]
}}

注意：上面的输出，session_id 为 会话数据给你的session_id,cate1、cate2、cate3分别为按照上面要求的进行的分类，VOC，我需要你采集用户和客服的实际会话数据，
用来佐证你的会话分类的正确性，每个会话可以产出多个分类

"""

if __name__ == "__main__":
#     source_table="data_mining.kf_dy_shop_xd_detail_topn"
    source_table=sys.argv[1]
#     channel="抖快系"
    channel=sys.argv[2]
#     channel_en="dy"
    channel_en=sys.argv[3]
#     ds="2025-03-26"
    ds=sys.argv[4]
    logger.info("输入source_table:{},channel:{},channel_en:{},ds:{}".format(source_table,channel,channel_en,ds))
    df_voc=read_voc(source_table,channel,ds)
    item_ids=df_voc["item_id"].unique().tolist()
    
    # 每次重跑剔除之前的数据
    sql_dd="""ALTER TABLE data_mining.kefu_session_xd_category123 DROP IF EXISTS PARTITION (ds="{}",channel="{}")""".format(ds,channel_en)
    spark.sql(sql_dd)
    
    for item_id in item_ids:
        logger.info("正在处理商品:{}".format(item_id))
        item_pd=df_voc.loc[df_voc["item_id"]==int(item_id)][["session_id","session_message_content"]]
        item_name=df_voc.loc[df_voc["item_id"]==int(item_id)][["item_name"]]["item_name"].unique().tolist()[0]
        shop_name=df_voc.loc[df_voc["item_id"]==int(item_id)][["shop_name"]]["shop_name"].unique().tolist()[0]
        item_pd["session_content"]=item_pd["session_message_content"].apply(lambda row:query_parser(row))
        result_dict = item_pd[["session_id","session_content"]].to_dict(orient='records')
        batch=20
        batch_num=len(result_dict)//batch
        cates=[]
        response_list=[]
        try:
            for i in range(batch_num+1):
                sessions=result_dict[batch*i:batch*(i+1)]
                if len(sessions)==0:
                    break
                session_user=[{"session_id":dd["session_id"],"session_content":extract_user_messages(dd["session_content"])} for dd in sessions]
                prompt_cate_full=prompt_cate.format(cate1,cate2,cate3,item_name,session_user)
        #         print(prompt_cate_full)
                response=llm_chat(prompt_cate_full)
                response_txt=response['data']["detail"]["choices"][0]["message"]["content"]
                cates_batch=json.loads(response_txt[response_txt.find("{"):response_txt.rfind("}")+1])
                cates=cates+cates_batch["session_analysis"]
                time.sleep(3)
        except Exception as e:
            logging.error(e)
            continue
            

        df=pd.DataFrame(cates).dropna()
        df["item_id"]=item_id
        df["item_name"]=item_name

        df=df[["item_id","item_name","session_id","cate1","cate2","cate3","VOC"]]
        df["session_id"]=df["session_id"].astype(int)
        df["VOC"]=df["VOC"].astype("string")

        if df.empty:
            continue
        solution_sp=spark.createDataFrame(df)
        solution_sp.createOrReplaceTempView("data_solution")
        sql0="""insert into table data_mining.kefu_session_xd_category123 PARTITION (ds="{}",channel="{}")
                select * from data_solution""".format(ds,channel_en)
        spark.sql(sql0)
        
        logger.info("写表成功:{}".format(item_id))